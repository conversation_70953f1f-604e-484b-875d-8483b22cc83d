import assert from 'assert';

const EXPRESS_ZENDESK_SUBDOMAIN = process.env.EXPRESS_ZENDESK_SUBDOMAIN;
const EXPRESS_ZENDESK_EMAIL = process.env.EXPRESS_ZENDESK_EMAIL;
const EXPRESS_ZENDESK_API_TOKEN = process.env.EXPRESS_ZENDESK_API_TOKEN;
const EXPRESS_GLADLY_DOMAIN = process.env.EXPRESS_GLADLY_DOMAIN;
const EXPRESS_GLADLY_API_TOKEN = process.env.EXPRESS_GLADLY_API_TOKEN;
assert(EXPRESS_ZENDESK_SUBDOMAIN, 'EXPRESS_ZENDESK_SUBDOMAIN is required');
assert(EXPRESS_ZENDESK_EMAIL, 'EXPRESS_ZENDESK_EMAIL is required');
assert(EXPRESS_ZENDESK_API_TOKEN, 'EXPRESS_ZENDESK_API_TOKEN is required');
assert(EXPRESS_GLADLY_DOMAIN, 'EXPRESS_GLADLY_DOMAIN is required');
assert(EXPRESS_GLADLY_API_TOKEN, 'EXPRESS_GLADLY_API_TOKEN is required');

const jsonHeaders = {
  'cache-control': 'no-cache, no-store, must-revalidate, max-age=0, s-maxage=0',
  'content-type': 'application/json'
};

function zendeskAuth() {
  const src = `${EXPRESS_ZENDESK_EMAIL}/token:${EXPRESS_ZENDESK_API_TOKEN}`;
  return { Authorization: `Basic ${Buffer.from(src).toString('base64')}` };
}

// Try different authentication methods for Gladly
function getGladlyAuth() {
  // Method 1: Bearer token (current)
  const bearerAuth = { Authorization: `Bearer ${EXPRESS_GLADLY_API_TOKEN}` };

  // Method 2: Basic auth with API key and colon (like Assembled)
  const basicAuth = { Authorization: `Basic ${Buffer.from(`${EXPRESS_GLADLY_API_TOKEN}:`).toString('base64')}` };

  // Method 3: API key in header (some APIs use this)
  const apiKeyAuth = { 'X-API-Key': EXPRESS_GLADLY_API_TOKEN };

  // For now, let's try Bearer first, but we can switch if needed
  return bearerAuth;
}

const gladlyAuth = getGladlyAuth();
async function fetchArticle(id, locale) {
  const url = `https://${EXPRESS_ZENDESK_SUBDOMAIN}.zendesk.com/api/v2/help_center/${locale}/articles/${id}.json`;
  const res = await fetch(url, { headers: zendeskAuth() });
  if (!res.ok) throw new Error(`Zendesk ${res.status} fetching article ${id}`);
  return (await res.json()).article;
}

async function gladlyGet(id) {
  const url = `${EXPRESS_GLADLY_DOMAIN}/api/v1/answers/${id}`;
  console.log(`Gladly GET request: ${url}`);
  console.log(`Gladly auth headers:`, gladlyAuth);

  const res = await fetch(url, { headers: gladlyAuth });
  console.log(`Gladly GET response status: ${res.status}`);

  if (res.status === 404) return null;
  if (!res.ok) {
    const responseText = await res.text();
    console.log(`Gladly GET error response: ${responseText}`);
    throw new Error(`Gladly ${res.status} GET answer ${id}: ${responseText}`);
  }
  return res.json();
}

async function gladlyCreate(article) {
  const url = `${EXPRESS_GLADLY_DOMAIN}/api/v1/answers`;
  const payload = {
    id: String(article.id),
    name: article.title,
    audience: 'Public',
  };

  console.log(`Gladly CREATE request: ${url}`);
  console.log(`Gladly CREATE payload:`, payload);

  const res = await fetch(url, {
    method: 'POST',
    headers: { ...gladlyAuth, 'content-type': 'application/json' },
    body: JSON.stringify(payload),
  });

  console.log(`Gladly CREATE response status: ${res.status}`);

  if (!res.ok) {
    const responseText = await res.text();
    console.log(`Gladly CREATE error response: ${responseText}`);
    throw new Error(`Gladly ${res.status} creating answer ${article.id}: ${responseText}`);
  }
}

async function gladlyUpsertContent(article) {
  const url = `${EXPRESS_GLADLY_DOMAIN}/api/v1/answers/${article.id}/languages/${article.locale}/type/html`;
  const res = await fetch(url, {
    method: 'PUT',
    headers: { ...gladlyAuth, 'content-type': 'application/json' },
    body: JSON.stringify({ title: article.title, bodyHtml: article.body }),
  });
  if (!res.ok) throw new Error(`Gladly ${res.status} PUT content for ${article.id}`);
}

async function gladlyDelete(id) {
  const res = await fetch(`${EXPRESS_GLADLY_DOMAIN}/api/v1/answers/${id}`, {
    method: 'DELETE',
    headers: gladlyAuth,
  });
  if (![204, 404].includes(res.status))
    throw new Error(`Gladly ${res.status} deleting answer ${id}`);
}

export const handler = async (req) => {
  console.log(`POST /guide/webhook ${new Date().toISOString()}`);

  // Debug environment variables
  console.log(`EXPRESS_GLADLY_DOMAIN: ${EXPRESS_GLADLY_DOMAIN}`);
  console.log(`EXPRESS_GLADLY_API_TOKEN: ${EXPRESS_GLADLY_API_TOKEN ? '[REDACTED]' : 'NOT SET'}`);

  let payload;
  try { payload = JSON.parse(req.body); }
  catch { return { statusCode: 400, headers: jsonHeaders, body: JSON.stringify({ message: 'Invalid JSON' }) }; }
  console.log(JSON.stringify(payload, null, 2));

  // Extract data from the actual Zendesk Guide event structure
  const { type, detail, event } = payload;

  if (!type || !detail?.id) {
    return { statusCode: 400, headers: jsonHeaders, body: JSON.stringify({ message: 'type and detail.id are required' }) };
  }

  const articleId = detail.id;

  try {
    if (type === 'zen:event-type:article.published') {
      // Handle published event
      const locale = event.locale;
      if (!locale) {
        return { statusCode: 400, headers: jsonHeaders, body: JSON.stringify({ message: 'locale required for published events' }) };
      }

      // Fetch the full article data from Zendesk
      const article = await fetchArticle(articleId, locale);

      // Check if answer already exists in Gladly
      const existing = await gladlyGet(articleId);

      // Create answer if it doesn't exist
      if (!existing) {
        await gladlyCreate(article);
      }

      // Upsert the content
      await gladlyUpsertContent(article);

      // If answer already existed, update the name in case it changed
      if (existing) {
        await fetch(`${EXPRESS_GLADLY_DOMAIN}/api/v1/answers/${articleId}`, {
          method: 'PATCH',
          headers: { ...gladlyAuth, 'content-type': 'application/json' },
          body: JSON.stringify({ name: article.title }),
        });
      }

      return { statusCode: 200, headers: jsonHeaders, body: JSON.stringify({ message: 'Article published and synced to Gladly' }) };

    } else if (type === 'zen:event-type:article.unpublished') {
      // Handle unpublished event - delete from Gladly
      await gladlyDelete(articleId);
      return { statusCode: 200, headers: jsonHeaders, body: JSON.stringify({ message: 'Article unpublished and removed from Gladly' }) };

    } else {
      // Unsupported event type
      console.log(`Ignoring unsupported event type: ${type}`);
      return { statusCode: 200, headers: jsonHeaders, body: JSON.stringify({ message: 'Event type not supported' }) };
    }

  } catch (err) {
    console.error(err);
    return { statusCode: 500, headers: jsonHeaders, body: JSON.stringify({ message: 'Server error' }) };
  }
};
